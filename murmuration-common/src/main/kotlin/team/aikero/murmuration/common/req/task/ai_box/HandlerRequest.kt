package team.aikero.murmuration.common.req.task.ai_box

import team.aikero.murmuration.common.enums.task.ClothingType
import team.aikero.murmuration.common.vo.*

data class HandRepairHandlerRequest(
    /**
     * 需要修复的图片
     */
    val inputImage: String,
    /**
     * 需要的数量
     */
    val count: Int
)

data class SmartCuttingHeadHandlerRequest(
    /**
     * 需要裁剪的图片
     */
    val image: String,
    /**
     * 裁剪的头部位置
     */
    val topLoc: TopLoc,
    /**
     * 比例
     */
    val size: CuttingSize,
    /**
     * 生图数量
     */
    val count: Int
)

data class TextEditHandlerRequest(
    /**
     * 需要编辑的图片
     */
    val image: String,
    /**
     * 用户编辑的提示词
     */
    val userPrompt: String,
    /**
     * 生图数量
     */
    val count: Int
)

data class RemoveWatermarkHandlerRequest(
    val imageUrl: String
)

/**
 * 超分（4k）请求
 */
data class FourKHandlerRequest(
    /**
     * 图片URL
     */
    val imageUrl: String,
)


open class ChangeBackgroundHandlerRequest(
    /**
     * 图片链接
     */
    open val imageUrl: String,

    /**
     * 生图数量
     */
    open val batchSize: Int
)

data class ChangeBackgroundHandlerInputRequest(
    override val imageUrl: String,
    override val batchSize: Int,
    val scenePicture: ScenePictureVo,
) : ChangeBackgroundHandlerRequest(imageUrl, batchSize)

open class PostureFissionHandlerRequest(
    /**
     * 图片链接
     */
    open val imageUrl: String,
)

data class PostureFissionHandlerInputRequest(
    override val imageUrl: String,
    /**
     * 姿势信息
     */
    val posture: PostureVo
) : PostureFissionHandlerRequest(imageUrl)


open class TryOnHandlerRequest(
    /**
     * 商品衣服图
     */
    open val clothImageUrl: String,

    /**
     * 款式信息
     */
    open val styleInfo: StyleInfoVo,

    /**
     * 启用MJ（打开AI生成参考图），true/false
     */
    open val usingMj: Boolean = false,

    /**
     * 风格管理信息，启用MJ才传
     */
    open val mjInfo: MjInfoVo?,

    /**
     * 字符串，可以有upper,lower,overall三种取值
     * 分别对应上半身，下半身，全身
     */
    open val clothLength: ClothingType,

    /**
     * 启用裂变，true/false
     */
    open val usingkontext: Boolean = false,

    /**
     * 姿势裂变生成张数
     */
    open val kontextBatchSize: Int? = null,

    /**
     * try on生成张数
     */
    open val tryonBatchSize: Int,

    /**
     * 姿势范围字典编码
     */
    open val poseRangeCode: String?= null,

    /**
     * 姿势范围
     */
    open val poseRange: List<String>? = null,
)

data class TryOnHandlerInputRequest(
    /**
     * 模特参考图
     */
    val modelImage: ModelReferenceImageVo? = null,

    /**
     * 商品衣服图
     */
    override val clothImageUrl: String,

    /**
     * 款式信息
     */
    override val styleInfo: StyleInfoVo,

    /**
     * 启用MJ，true/false
     */
    override val usingMj: Boolean = false,

    /**
     * 风格管理信息，启用MJ才传
     */
    override val mjInfo: MjInfoVo? = null,

    /**
     * 字符串，可以有upper,lower,overall三种取值
     * 分别对应上半身，下半身，全身
     */
    override val clothLength: ClothingType,

    /**
     * 启用裂变，true/false
     */
    override val usingkontext: Boolean = false,

    /**
     * 目标模特图
     */
    val targetModelFaceImage: String? = null,

    /**
     * 背景图
     */
    val backgroundImage: BackgroundImageVo? = null,

    /**
     * 姿势裂变生成张数
     */
    override val kontextBatchSize: Int? = null,

    /**
     * try on生成张数
     */
    override val tryonBatchSize: Int,

    /**
     * 姿势范围字典编码
     */
    override val poseRangeCode: String?= null,

    /**
     * 姿势范围
     */
    override val poseRange: List<String>? = null,
) : TryOnHandlerRequest(clothImageUrl, styleInfo, usingMj, mjInfo, clothLength, usingkontext, kontextBatchSize, tryonBatchSize, poseRangeCode, poseRange)

/**
 * AE上架属性转换请求
 */
data class AeAttributeConvertHandlerRequest(
    /**
     * 产品ID
     */
    val id: String,

    /**
     * 产品名称
     */
    val productName: String,

    /**
     * 产品类型
     */
    val productType: String,

    /**
     * 产品信息
     */
    val productInfo: String,

    /**
     * 主图URL
     */
    val masterImgUrl: String,

    /**
     * Muse属性映射表URL
     */
    val museAttrXlsxUrl: String,

    /**
     * AE批量模板URL
     */
    val aeTemplateXlsxUrl: String,
)
