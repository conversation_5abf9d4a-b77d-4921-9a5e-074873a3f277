package team.aikero.murmuration.common.req.task

import java.math.BigDecimal

/**
 * @Author: caicijie
 * @description:
 * @Date: 2025/8/26 17:08
 */
data class AlibabaDistributionRequest(
    /**
     * 产品名称
     */
    val productName: String,

    /**
     * 产品品类
     */
    val productCategory: String,

    /**
     * SPU轮播图URLs
     */
    val spuCarouselUrls: String,

    /**
     * SPU详情图URLs
     */
    val spuDetailUrls: String,

    /**
     * SKC图片URLs
     */
    val skcUrls: String,
)
