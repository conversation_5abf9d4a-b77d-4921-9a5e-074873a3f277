package team.aikero.murmuration.core

import team.aikero.murmuration.common.annotation.EnumComment

/**
 * 能力类型
 *
 * 模型功能、接口功能、算法、代码逻辑
 *
 * <AUTHOR>
 */
@EnumComment
enum class Ability {
    /**
     * 开始
     */
    START,

    /**
     * 结束
     */
    END,

    /**
     * 图案衍生
     */
    IMAGE_DERIVATION,

    /**
     * 超分
     */
    UPSCALE,

    /**
     * 模特图衍生
     */
    MODEL_IMAGE_DERIVATION,

    /**
     * 图套贴图
     */
    TEXTURE,

    /**
     * 姿势裂变
     */
    POSTURAL_FISSION,

    /**
     * 抠图
     */
    CUTOUTS,

    /**
     * 换脸
     */
    CHANGE_FACE,

    /**
     * 换背景
     */
    CHANGE_BACKGROUND,

    /**
     * 服装上身
     */
    TRY_ON,

    /**
     * 推送POD选款
     */
    PUSH_POD_STYLE,

    /**
     * 图案提取
     */
    PATTERN_EXTRACTION,

    /**
     * 手部修复
     */
    AI_BOX_HAND_REPAIR,

    /**
     * 文本编辑
     */
    AI_BOX_TEXT_EDIT,

    /**
     * 智能裁头
     */
    AI_BOX_SMART_CUTTING_HEAD,

    /**
     * 超分（4k）
     */
    FOUR_K,

    /**
     * 去水印
     */
    REMOVE_WATERMARK,

    /**
     * AE上架属性转换
     */
    AE_PUTAWAY_ATTRIBUTE_CONVERT,

    /**
     * SKC颜色识别
     */
    SKC_COLOR_IDENTIFICATION,

    /**
     * 图片裁剪
     */
    IMAGE_CROPPING,

    /**
     * 1688铺货（图包生成）
     */
    ALIBABA_DISTRIBUTION,
}

fun Ability.getTitle(): String {
    return ""
}
