package team.aikero.murmuration.core.util

import cn.hutool.extra.spring.SpringUtil
import team.aikero.blade.sequence.code.entity.CodeRule
import team.aikero.blade.sequence.code.entity.SimpleCodeRule
import team.aikero.blade.sequence.code.generate.BusinessCodeGenerator

object CodeGenerator {
    private val businessCodeGenerator: BusinessCodeGenerator by lazy {
        SpringUtil.getBean(
            BusinessCodeGenerator::class.java
        )
    }

    fun next(codeRule: CodeRule): String {
        return businessCodeGenerator.generate(codeRule)
    }

}

/**
 * 编号生成规则
 * <AUTHOR>
 * @date 2024/9/4 15:34
 */
enum class CodeRule(
    /**
     * 编码值的前缀 作为生成编码的一部分 例如 F FH FASHION
     */
    override val valuePrefix: String,

    /**
     * 编码名称用途 会拼接到生成编码的redis key当中作为一部分 注意这部分不作为编码的一部分 而只是作为redis key的一部分
     */
    override val codeName: String,

    /**
     * 日期的前缀 作为编码的一部分 例如 yyMMdd yy
     */
    override val datePrefix: String,

    /**
     * 规则 例如 %1$05d  自增为1 最后会与redis生成的实际值做替换 得到 00001
     */
    override val formatTemplate: String,

    /**
     * 针对这个缓存key的描述 说明清楚用途 与codeName对应
     */
    override val desc: String,
) : SimpleCodeRule {

    AI_BOX("", "AI_BOX", "yyMMdd", "%1$04d", "AIBox任务编号"),

}