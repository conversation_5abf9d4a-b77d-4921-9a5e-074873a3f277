package team.aikero.murmuration.core.workflow.entity

import com.fasterxml.jackson.databind.JsonNode
import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.Serialized
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId

@Entity
interface TaskInstanceResult : LongId, CreatedTime {

    @ManyToOne
    val task: TaskInstance?

    /**
     * 图片URL（可选）
     * 当任务结果是图片时使用
     */
    val url: String?

    /**
     * 是否验收通过
     */
    @Default("false")
    val passed: Boolean

    /**
     * 任务结果属性（可选）
     * 当任务结果包含结构化数据时使用
     */
    @Serialized
    val attributes: JsonNode?
}

/**
 * 统一的任务结果结构
 *
 * 支持url和attributes的可选组合，为了解决url和attributes可空的情况
 *
 * <AUTHOR>
 */
data class SimpleTaskResult(
    /**
     * 任务ID
     */
    val taskId: Long,

    /**
     * 结果ID
     */
    val id: Long,

    /**
     * 图片URL（可选）
     * 当任务结果包含图片时使用
     */
    val url: String? = null,

    /**
     * 任务结果属性（可选）
     * 当任务结果包含结构化数据时使用
     */
    val attributes: JsonNode? = null,

    /**
     * 结果类型标识
     */
    val type: TaskResultType = when {
        url != null && attributes != null -> TaskResultType.FULL
        url != null -> TaskResultType.URL_ONLY
        attributes != null -> TaskResultType.ATTRIBUTES_ONLY
        else -> TaskResultType.EMPTY
    }
) {
    init {
        require(url != null || attributes != null) {
            "任务结果必须包含url或attributes中的至少一项"
        }
    }

    /**
     * 检查是否有URL结果
     */
    fun hasUrl(): Boolean = url != null

    /**
     * 检查是否有属性结果
     */
    fun hasAttributes(): Boolean = attributes != null

    /**
     * 安全获取URL（如果存在）
     */
    fun fetchUrl(): String = url ?: throw IllegalArgumentException("URL结果不存在")

    /**
     * 安全获取属性（如果存在）
     */
    fun fetchAttributes(): JsonNode = attributes ?: throw IllegalArgumentException("属性结果不存在")
}

/**
 * 任务结果类型枚举
 */
enum class TaskResultType {
    /**
     * 仅包含URL
     */
    URL_ONLY,

    /**
     * 仅包含属性
     */
    ATTRIBUTES_ONLY,

    /**
     * 包含URL和属性
     */
    FULL,

    /**
     * 空结果（不应该出现）
     */
    EMPTY
}

/**
 * TaskInstanceResult扩展函数
 */
fun TaskInstanceResult.toSimpleResult(): SimpleTaskResult {
    return SimpleTaskResult(
        taskId = this.task?.id!!,
        id = this.id,
        url = this.url,
        attributes = this.attributes
    )
}

/**
 * TaskInstanceResult列表扩展函数
 */
fun List<TaskInstanceResult>.toSimpleResults(): List<SimpleTaskResult> {
    return this.map { it.toSimpleResult() }
}
