package team.aikero.murmuration.component

import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.util.AssertionErrors.assertNotNull
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.service.component.ImageInfoFetchException
import team.aikero.murmuration.service.component.ImageInfoFetcher

@SpringBootTest(classes = [MurmurationApplication::class])
class ImageInfoFetcherTest(@Autowired val imageInfoFetcher: ImageInfoFetcher) {

    @Test
    fun fetchSuccessful() {
        val (height, width) = imageInfoFetcher.fetch("https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png")
        assertThat(height).isEqualTo(1030)
        assertThat(width).isEqualTo(841)
    }

    @Test
    fun fetchFailed() {
        assertThatThrownBy {
            imageInfoFetcher.fetch("https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png1")
        }.isInstanceOf(ImageInfoFetchException::class.java)
    }

    @Test
    fun fetchSpecialURL(){
        val (height, width) = imageInfoFetcher.fetch("https://oss.yunbanfang.cn/{8981797c-848c-4c5b-91e5-6a05c8862c87}/tiangong_f5b347f9a96e4a7b8a30a468d657fd0f.PNG")
        assertNotNull("Height should not be null", height)
        assertNotNull("Width should not be null", width)
    }
}
