package team.aikero.murmuration.service.controller.web

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.springframework.util.Assert
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.ComplexPageParam
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.data.jimmer.findPage
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.murmuration.common.req.task.ai_box.*
import team.aikero.murmuration.common.vo.BackgroundImageVo
import team.aikero.murmuration.common.vo.MjInfoVo
import team.aikero.murmuration.common.vo.ModelReferenceImageVo
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.material.dto.AIBoxInfo
import team.aikero.murmuration.core.material.dto.AIBoxPage
import team.aikero.murmuration.core.material.dto.AIBoxPageReq
import team.aikero.murmuration.core.util.CodeGenerator
import team.aikero.murmuration.core.util.CodeRule
import team.aikero.murmuration.core.workflow.entity.AIBoxTask
import team.aikero.murmuration.core.workflow.entity.TaskStatus
import team.aikero.murmuration.core.workflow.entity.id
import team.aikero.murmuration.core.workflow.task.TaskManager
import team.aikero.murmuration.event.listener.AIBoxTaskNotification
import team.aikero.murmuration.infra.rocketmq.AIBoxTopicHolder
import team.aikero.murmuration.infra.rocketmq.Message
import team.aikero.murmuration.infra.rocketmq.RocketMQClient
import tech.tiangong.bfg.common.req.ModelLibraryLastUsedTimeReq
import tech.tiangong.bfg.sdk.client.FmModelLibraryClient
import java.time.LocalDateTime

@RestController
@RequestMapping("/web/ai-box/task")
class AIBoxTaskController(
    private val sql: KSqlClient,
    private val taskManager: TaskManager,
    private val rocketMQClient: RocketMQClient,
    private val fmModelLibraryClient: FmModelLibraryClient,
) {

    /**
     * 获取ai box任务分页
     */
    @PostMapping("/page")
    fun page(
        @RequestBody req: ComplexPageParam<AIBoxPageReq>
    ): DataResponse<PageVo<AIBoxPage>> =
        ok(
            sql.findPage(req, AIBoxPage::class)
        )

    /**
     * 获取ai box任务详细
     */
    @GetMapping("/{id}")
    fun getInfo(
        @PathVariable id: Long
    ): DataResponse<AIBoxInfo> =
        ok(
            sql.findOneById(AIBoxInfo::class, id)
        )

    /**
     * 批量获取ai box任务详细
     * @param batchIds aiBoxId集合
     */
    @PostMapping("/batch")
    fun getInfoByBatch(
        @RequestBody batchIds: List<Long>
    ): DataResponse<List<AIBoxInfo>> =
        ok(
            sql.executeQuery(AIBoxTask::class) {
                where(table.id valueIn batchIds)
                select(table.fetch(AIBoxInfo::class))
            }
        )

    /**
     * 创建手部修复任务
     */
    @PostMapping("/hand-repair")
    fun createHandRepairTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody req: List<HandRepairRequest>,
    ): DataResponse<List<Long>> =
        handleSimpleTaskRequests(taskSource, supplier, Ability.AI_BOX_HAND_REPAIR, req)

    /**
     * 创建智能剪头任务
     */
    @PostMapping("/smart-cutting-head")
    fun createSmartCuttingHeadTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<SmartCuttingHeadRequest>,
    ): DataResponse<List<Long>> =
        handleSimpleTaskRequests(taskSource, supplier, Ability.AI_BOX_SMART_CUTTING_HEAD, request)

    /**
     * 创建文字编辑任务
     */
    @PostMapping("/text-edit")
    fun createTextEditTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<TextEditRequest>,
    ): DataResponse<List<Long>> =
        handleSimpleTaskRequests(taskSource, supplier, Ability.AI_BOX_TEXT_EDIT, request)

    /**
     * 创建去水印任务
     */
    @PostMapping("/remove-watermark")
    fun createRemoveWatermarkTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<RemoveWatermarkRequest>,
    ): DataResponse<List<Long>> =
        handleCountBasedTaskRequests(taskSource, supplier, Ability.REMOVE_WATERMARK, request) { it.count }

    /**
     * 创建超分任务
     */
    @PostMapping("/four-k")
    fun createFourKTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<FourKRequest>,
    ): DataResponse<List<Long>> =
        handleCountBasedTaskRequests(taskSource, supplier, Ability.FOUR_K, request) { it.count }

    /**
     * 创建姿势裂变任务
     */
    @PostMapping("/posture")
    fun createPostureTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<PostureRequest>,
    ): DataResponse<List<Long>> =
        handleListBasedTaskRequests(taskSource, supplier, Ability.POSTURAL_FISSION, request) { req ->
            req.postureList.map { posture ->
                PostureFissionHandlerInputRequest(req.handlerRequest.imageUrl, posture)
            }
        }

    /**
     * 创建换背景任务
     */
    @PostMapping("/change-background")
    fun createChangeBackgroundTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<ChangeBackgroundRequest>,
    ): DataResponse<List<Long>> =
        handleListBasedTaskRequests(taskSource, supplier, Ability.CHANGE_BACKGROUND, request) { req ->
            req.scenePictureList.map { scenePicture ->
                ChangeBackgroundHandlerInputRequest(
                    req.handlerRequest.imageUrl,
                    req.handlerRequest.batchSize,
                    scenePicture
                )
            }
        }


    /**
     * 创建tryon任务
     */
    @PostMapping("/try-on")
    fun createTryOnTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<TryOnRequest>,
    ): DataResponse<List<Long>> =
        handleListBasedTaskRequests(taskSource, supplier, Ability.TRY_ON, request) { req ->
            val handlerRequest = req.handlerRequest
            val usingMj = handlerRequest.usingMj
            if (usingMj) {  //启用MJ（打开AI生成参考图）不需要参考图
                Assert.isTrue(handlerRequest.mjInfo.isNotNull(), "风格管理信息不能为空")
                generateTryOnRequestsForMj(handlerRequest, req)
            } else {
                Assert.isTrue(req.modelImageList.isNotEmpty(), "参考图不能为空")
                updateModelImageLastUsedTime(req.modelImageList!!.mapNotNull { it.modelLibraryId })
                generateTryOnRequestsForNotMj(handlerRequest, req)
            }
        }

    /**
     * 处理简单的任务请求（一对一）
     */
    private fun <T : TaskRequest<*>> handleSimpleTaskRequests(
        taskSource: TaskSource,
        supplier: Supplier,
        ability: Ability,
        requests: List<T>
    ): DataResponse<List<Long>> {
        val taskIds = mutableListOf<Long>()

        requests.forEach { request ->
            val aiBoxTaskId = createAIBoxTask(request.bizId, taskSource, supplier, ability)
            createAndAddTask(request, supplier, ability, request.handlerRequest!!, aiBoxTaskId, taskSource, taskIds)
        }

        return ok(taskIds)
    }

    /**
     * 处理基于数量的任务请求（一对多，基于count）
     */
    private fun <T : TaskRequest<*>> handleCountBasedTaskRequests(
        taskSource: TaskSource,
        supplier: Supplier,
        ability: Ability,
        requests: List<T>,
        countExtractor: (T) -> Int
    ): DataResponse<List<Long>> {
        val taskIds = mutableListOf<Long>()

        requests.forEach { request ->
            val aiBoxTaskId = createAIBoxTask(request.bizId, taskSource, supplier, ability)
            val count = countExtractor(request)

            repeat(count) {
                createAndAddTask(request, supplier, ability, request.handlerRequest!!, aiBoxTaskId, taskSource, taskIds)
            }
        }

        return ok(taskIds)
    }

    /**
     * 处理基于列表的任务请求（一对多，基于列表）
     */
    private fun <T : TaskRequest<*>> handleListBasedTaskRequests(
        taskSource: TaskSource,
        supplier: Supplier,
        ability: Ability,
        requests: List<T>,
        handlerRequestGenerator: (T) -> List<Any>
    ): DataResponse<List<Long>> {
        val taskIds = mutableListOf<Long>()

        requests.forEach { request ->
            val aiBoxTaskId = createAIBoxTask(request.bizId, taskSource, supplier, ability)
            val handlerRequests = handlerRequestGenerator(request)

            handlerRequests.forEach { handlerRequest ->
                createAndAddTask(request, supplier, ability, handlerRequest, aiBoxTaskId, taskSource, taskIds)
            }
        }

        return ok(taskIds)
    }

    /**
     * 创建任务并添加到任务ID列表
     */
    private fun <T : TaskRequest<*>> createAndAddTask(
        request: T,
        supplier: Supplier,
        ability: Ability,
        handlerRequest: Any,
        aiBoxTaskId: Long,
        taskSource: TaskSource,
        taskIds: MutableList<Long>
    ) {
        if (taskSource != TaskSource.AI_BOX) {
            require(request.bizId != null) {
                "业务流过来的AI_BOX任务需要bizId"
            }
        }
        taskManager.createTask(
            supplier = supplier,
            ability = ability,
            request = handlerRequest,
            aiBoxTaskId = aiBoxTaskId
        ).also(taskIds::add)
    }

    /**
     * 创建AIBox任务
     */
    private fun createAIBoxTask(
        taskSourceId: String?,
        taskSource: TaskSource,
        supplier: Supplier,
        ability: Ability,
    ): Long {
        if (taskSource != TaskSource.AI_BOX && taskSourceId == null) {
            throw BusinessException("业务流过来的AI_BOX任务需要bizId")
        }
        val aiBoxTaskId = sql.save(
            AIBoxTask {
                this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                this.taskSourceId = taskSourceId
                this.taskSource = taskSource
            }, SaveMode.INSERT_ONLY
        ).modifiedEntity.id
        if (taskSourceId != null) {
            sendMq(taskSourceId, taskSource, supplier, ability, aiBoxTaskId)
        }
        return aiBoxTaskId
    }

    private fun sendMq(
        taskSourceId: String,
        taskSource: TaskSource,
        supplier: Supplier,
        ability: Ability,
        aiBoxTaskId: Long
    ) {
        val messageBody = AIBoxTaskNotification(
            aiBoxTaskId = aiBoxTaskId,
            taskSourceId = taskSourceId,
            supplier = supplier,
            ability = ability,
            status = TaskStatus.RUNNING,
        )

        val message = Message(
            topic = AIBoxTopicHolder.topic,
            keys = listOf("$aiBoxTaskId"),
            tag = "$ability:$supplier",
            payload = messageBody,
        )
        rocketMQClient.send(message)
        log.info { "AIBox task $aiBoxTaskId send created mq" }
    }

    /**
     * 构建tryon处理请求
     */
    private fun buildTryOnHandlerInputRequest(
        handlerRequest: TryOnHandlerRequest,
        mjInfo: MjInfoVo? = null,
        modelImage: ModelReferenceImageVo? = null,
        targetModelFaceImage: String? = null,
        backgroundImage: BackgroundImageVo? = null,
        ) =
        TryOnHandlerInputRequest(
            modelImage = modelImage,
            clothImageUrl = handlerRequest.clothImageUrl,
            styleInfo = handlerRequest.styleInfo,
            usingMj = handlerRequest.usingMj,
            mjInfo = mjInfo,
            clothLength = handlerRequest.clothLength,
            usingkontext = handlerRequest.usingkontext,
            targetModelFaceImage = targetModelFaceImage,
            backgroundImage = backgroundImage,
            kontextBatchSize = handlerRequest.kontextBatchSize,
            tryonBatchSize = handlerRequest.tryonBatchSize,
            poseRangeCode = handlerRequest.poseRangeCode,
            poseRange = handlerRequest.poseRange,
        )

    /**
     * 生成tryon请求-启用MJ
     */
    private fun generateTryOnRequestsForMj(handlerRequest: TryOnHandlerRequest, req: TryOnRequest): List<TryOnHandlerInputRequest> {
        return generateCombinations(
            backgroundImages = req.backgroundImageList,
            targetModelFaces = req.targetModelFaceImageList
        ).map { (backgroundImage, targetModelFaceImage) ->
            buildTryOnHandlerInputRequest(
                handlerRequest = handlerRequest,
                mjInfo = handlerRequest.mjInfo,
                targetModelFaceImage = targetModelFaceImage,
                backgroundImage = backgroundImage
            )
        }.ifEmpty {
            listOf(buildTryOnHandlerInputRequest(handlerRequest = handlerRequest, mjInfo = handlerRequest.mjInfo))
        }
    }

    /**
     * 生成tryon请求-不启用MJ
     */
    private fun generateTryOnRequestsForNotMj(handlerRequest: TryOnHandlerRequest, req: TryOnRequest): List<TryOnHandlerInputRequest> {
        return req.modelImageList!!.flatMap { modelImage ->
            generateCombinations(
                backgroundImages = req.backgroundImageList,
                targetModelFaces = req.targetModelFaceImageList
            ).map { (backgroundImage, targetModelFaceImage) ->
                buildTryOnHandlerInputRequest(
                    handlerRequest = handlerRequest,
                    modelImage = modelImage,
                    targetModelFaceImage = targetModelFaceImage,
                    backgroundImage = backgroundImage
                )
            }.ifEmpty {
                listOf(buildTryOnHandlerInputRequest(handlerRequest = handlerRequest, modelImage = modelImage))
            }
        }
    }

    /**
     * 生成背景图和模特图的组合
     */
    private fun generateCombinations(
        backgroundImages: List<BackgroundImageVo>?,
        targetModelFaces: List<String>?
    ): List<Pair<BackgroundImageVo?, String?>> {
        return when {
            backgroundImages.isNotEmpty() && targetModelFaces.isNotEmpty() -> {
                backgroundImages!!.flatMap { bg -> targetModelFaces!!.map { model -> bg to model } }
            }
            backgroundImages.isNotEmpty() -> backgroundImages!!.map { it to null }
            targetModelFaces.isNotEmpty() -> targetModelFaces!!.map { null to it }
            else -> emptyList()
        }
    }

    /**
     * 更新模特参考图的最近使用时间
     */
    private fun updateModelImageLastUsedTime(idList: List<Long>) {
        try {
            fmModelLibraryClient.updateLastUsedTime(ModelLibraryLastUsedTimeReq(idList, LocalDateTime.now()))
        } catch (e: Exception) {
            log.error { "updateModelImageLastUsedTime fail id=${idList},error:${e.message}" }
        }
    }

}
