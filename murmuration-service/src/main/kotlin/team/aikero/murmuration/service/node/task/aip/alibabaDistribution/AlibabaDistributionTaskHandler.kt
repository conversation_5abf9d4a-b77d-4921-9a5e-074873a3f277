package team.aikero.murmuration.service.node.task.aip.alibabaDistribution

import team.aikero.murmuration.common.req.task.AlibabaDistributionRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult

/**
 * 1688铺货
 */
@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.ALIBABA_DISTRIBUTION
)
class AlibabaDistributionTaskHandler(
    val executor: AlibabaDistributionExecutor,
): TaskHandler<AlibabaDistributionRequest, String> {
    override fun create(request: AlibabaDistributionRequest): String {
        return executor.createTask(
            AlibabaDistributionInput(
                productName = request.productName,
                productCategory = request.productCategory,
                spuCarouselUrls = request.spuCarouselUrls,
                spuDetailUrls = request.spuDetailUrls,
                skcUrls = request.skcUrls
            )
        )
    }

    override fun query(request: AlibabaDistributionRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map {
            // 将转换结果作为属性返回，URL字段使用占位符
            listOf(TaskHandlerResult("alibaba_distribution-convert-result", it))
        }
    }

}
