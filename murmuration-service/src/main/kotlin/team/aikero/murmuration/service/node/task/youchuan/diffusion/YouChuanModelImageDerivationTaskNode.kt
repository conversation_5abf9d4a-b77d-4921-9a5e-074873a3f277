package team.aikero.murmuration.service.node.task.youchuan.diffusion

import team.aikero.murmuration.common.req.task.YouChuanDiffusionRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.entity.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.shared.Image
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.SimpleImage

/**
 * 悠船模特图衍生任务节点
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@NodeIdentifier(name = "悠船-模特图衍生", supplier = Supplier.MIDJOURNEY, ability = Ability.MODEL_IMAGE_DERIVATION)
class YouChuanModelImageDerivationTaskNode : TaskNode<YouChuanModelImageDerivationInput, YouChuanModelImageDerivationParameter, ImageListOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: YouChuanModelImageDerivationInput, parameter: YouChuanModelImageDerivationParameter) {
        check(parameter.n % 4 == 0) { "生图数量必须是4的倍数" }

        val bottomImageUrls = input.bottomImages?.map { it.getUrl() }
        val styleImageUrls = input.referenceStyleImages?.map { it.getUrl() }
        val roleImageUrls = input.referenceRoleImages?.map { it.getUrl() }

        // 循环创建任务
        repeat(parameter.n / 4) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.MIDJOURNEY,
                ability = Ability.MODEL_IMAGE_DERIVATION,
                request = YouChuanDiffusionRequest(
                    prompt = input.prompt,
                    referenceImageUrl = null,
                    bottomImageUrls = bottomImageUrls,
                    referenceStyleImageUrls = styleImageUrls,
                    referenceRoleImageUrls = roleImageUrls,
                    moodboardId = parameter.moodboardId,
                ),
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ImageListOutput {
        val images = taskResults.map {
            SimpleImage(it.fetchUrl())
        }
        return ImageListOutput(images)
    }
}

data class YouChuanModelImageDerivationInput(
    @NodeProperties("提示词")
    val prompt: String,

    @NodeProperties(name = "底图列表")
    val bottomImages: List<Image>?,

    @NodeProperties(name = "参照风格图列表")
    val referenceStyleImages: List<Image>?,

    // 版本=6时为参照角色图，版本=7时为参照万物图
    @NodeProperties(name = "参照角色/万物图列表")
    val referenceRoleImages: List<Image>?,
): Input

data class YouChuanModelImageDerivationParameter(
    @NodeProperties("生图数量")
    val n: Int = 4,

    @NodeProperties("风格ID")
    val moodboardId: String? = null,
): Parameter
