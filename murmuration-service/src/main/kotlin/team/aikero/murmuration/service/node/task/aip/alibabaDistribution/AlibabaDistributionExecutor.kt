package team.aikero.murmuration.service.node.task.aip.alibabaDistribution

import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.service.node.task.aip.AipClient
import team.aikero.murmuration.service.node.task.aip.AipExecutor
import team.aikero.murmuration.service.node.task.aip.AipExecutorIdentifier
import java.math.BigDecimal

@AipExecutorIdentifier(AipAbility.ALIBABA_DISTRIBUTION)
class AlibabaDistributionExecutor(client: AipClient): AipExecutor<AlibabaDistributionInput, AlibabaDistributionOutput>(client)

data class AlibabaDistributionInput(
    /**
     * 产品ID
     */
    val productId: String ?=null,

    /**
     * 产品名称
     */
    val productName: String,

    /**
     * 产品品类
     */
    val productCategory: String,

    /**
     * 产品属性列表
     */
    val productAttributeList: String?=null,

    /**
     * SPU主图URLs（可能为空）
     */
    val spuMainUrls: String?=null,

    /**
     * SPU轮播图URLs
     */
    val spuCarouselUrls: String,

    /**
     * SPU详情图URLs
     */
    val spuDetailUrls: String,

    /**
     * SKC图片URLs
     */
    val skcUrls: String,

    /**
     * 产品价格
     */
    val productPrice: BigDecimal?=null
)

data class AlibabaDistributionOutput(
    /**
     * 产品名称列表
     */
    val productName: List<String>?=null,

    /**
     * SPU图片URL列表
     */
    val spuUrls: List<String>?=null,

    /**
     * 白底图URL列表
     */
    val whiteBackgroundUrls: List<String>?=null,

    /**
     * SKC图片URL列表
     */
    val skcUrls: List<String>?=null,

    /**
     * 尺码表图片URL列表
     */
    val sizechartUrls: List<String>?=null,

    /**
     * 验证状态 (1表示有效)
     */
    val valid: Int
)
