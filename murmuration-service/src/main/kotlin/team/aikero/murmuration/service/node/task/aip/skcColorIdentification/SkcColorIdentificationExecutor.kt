package team.aikero.murmuration.service.node.task.aip.skcColorIdentification

import team.aikero.murmuration.service.node.task.aip.*

@AipExecutorIdentifier(AipAbility.SKC_COLOR_IDENTIFICATION)
class SkcColorIdentificationExecutor(client: AipClient): AipExecutor<SkcColorIdentificationInput, SkcColorIdentificationOutput>(client) {
    override fun validateTaskOutput(output: SkcColorIdentificationOutput) {
        val errorMessages = mutableListOf<String>()
        if (output.colour.isBlank()) {
            errorMessages.add("colour 不能为空")
        }
        if (output.spuid.isBlank()) {
            errorMessages.add("spuid 不能为空")
        }
        if (output.skcid.isBlank()) {
            errorMessages.add("skcid 不能为空")
        }
        if (errorMessages.isNotEmpty()) {
            throw InvalidTaskOutputException(errorMessages.joinToString("; "))
        }
    }
}

data class SkcColorIdentificationInput(
    /**
     * 图片URL
     */
    val url: String,

    /**
     * 品类
     */
    val pinlei: String,

    /**
     * 输出图片URL
     */
    val outputUrl: String?,

    /**
     * SPU ID
     */
    val spuid: String,

    /**
     * SKC ID
     */
    val skcid: String,
)

data class SkcColorIdentificationOutput(
    /**
     * 识别的颜色
     */
    val colour: String,

    /**
     * 输出图片URL
     */
    val outputUrl: String?,

    /**
     * SPU ID
     */
    val spuid: String,

    /**
     * SKC ID
     */
    val skcid: String,
)