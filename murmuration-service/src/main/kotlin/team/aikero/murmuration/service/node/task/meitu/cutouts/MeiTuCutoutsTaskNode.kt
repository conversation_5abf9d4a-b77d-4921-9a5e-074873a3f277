package team.aikero.murmuration.service.node.task.meitu.cutouts

import cn.hutool.core.bean.BeanUtil
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.entity.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.GraphicImage
import team.aikero.murmuration.service.node.shared.ImageListInput
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.graphic.GraphicStorehouseType
import team.aikero.murmuration.service.node.shared.node.SmartGraphicStorehouseTaskNode
import team.aikero.murmuration.service.node.task.meitu.MeiTuParameterReq

/**
 * 扣图任务节点
 */
@NodeIdentifier(name = "美图抠图", supplier = Supplier.MEI_TU, ability = Ability.CUTOUTS)
class MeiTuCutoutsTaskNode :
    SmartGraphicStorehouseTaskNode<ImageListInput, MeiTuCutoutsTaskNodeParameter, ImageListOutput>() {

    override fun createTask(
        context: WorkflowNodeContext,
        input: ImageListInput,
        parameter: MeiTuCutoutsTaskNodeParameter
    ) {
        val graphicImages = input.images.toGraphicImage()

        for (image in graphicImages) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.MEI_TU,
                ability = Ability.CUTOUTS,
                request = MeiTuCutoutsReq(
                    image.getUrl(),
                    BeanUtil.copyProperties(parameter, MeiTuParameterReq::class.java)
                ),
                storage = image,
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ImageListOutput {
        val storage = taskManager.readTaskStorage<GraphicImage>(context.node.id)

        val graphicImages = taskResults.map {
            val originImage = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            detectAndConvert(it.fetchUrl(), GraphicStorehouseType.TRANSPARENT_SEG_IMAGE, originImage)
        }.referSelect()
            .saveToGraphicImage()

        return ImageListOutput(graphicImages)
    }

}

class MeiTuCutoutsTaskNodeParameter : Parameter, MeiTuParameterReq()
