package team.aikero.murmuration.service.node.task.openbridge

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue
import team.aikero.blade.core.exception.BusinessException

/**
 * 图片检测请求
 */
data class ImageModerationRequest(
    /**
     * 请求ID（UUID）
     * <p>
     * 方便溯源排查问题
     */
    val requestId: String,

    /**
     * 图片链接
     */
    val image: String,

    /**
     * 指定检测项
     * <p>
     * 不能同时指定海外跟国内服检测服务，要么全部指定国内服务，要么全部指定海外服务
     * <p>
     * baselineCheck：通用基线检测
     * baselineCheck_pro：通用基线检测_专业版
     * baselineCheck_cb：通用基线检测_出海版
     * tonalityImprove：内容治理检测
     * tonalityImprove_cb：内容治理检测_出海版
     * aigcCheck：AIGC图片风险
     * aigcCheck_cb：AIGC图片风险检测_出海版
     * profilePhotoCheck：头像图片检测
     * postImageCheck：帖子评论图片检测
     * advertisingCheck：营销素材检测
     * liveStreamCheck：视频\直播截图检测
     * riskDetection：恶意图片检测
     */
    val serviceList: List<String>,
)

/**
 * 图片裁剪入参
 */
data class imageCroppingRequest(
    /**
     * 图片链接
     * <p>
     * 图片尺寸应该大于100*100像素，小于5000*5000像素
     */
    val imageUrl: String,
    /**
     * 裁剪宽度
     */
    val width : Int,
    /**
     * 裁剪高度
     */
    val height : Int
)

/**
 * 图片裁剪返参
 */
data class imageCroppingRequestData(
    /**
     * 请求 ID
     * 用来识别唯一一次请求调用，API供应商生成
     */
    val requestId: String?=null,
    /**
     * 图片链接
     */
    val imageUrl : String?=null,
)

data class Response<T>(
    val successful: Boolean,
    val code: String,
    val message: String?,
    val data: T?,
)

data class ImageModerationData(val resultList: List<Result>) {
    /**
     * 检测结果
     */
    data class Result(
        val riskLevel: RiskLevel,
        val labelList: List<Label>?,
    )

    /**
     * 风险标签
     */
    data class Label(
        val label: String?,
        val confidence: String?,
        val description: String?,
    )

    /**
     * 风险等级
     */
    enum class RiskLevel(
        @JsonValue val value: String,
        val level: Int,
    ) {
        HIGH(value = "high", level = 3),
        MEDIUM(value = "medium", level = 2),
        LOW(value = "low", level = 1),
        NONE(value = "none", level = 0);

        fun isHigherThan(other: RiskLevel) = this.level > other.level
    }
}

data class RemoveWatermarkRequest(
    val imageUrl: String
)

data class RemoveWatermarkData(
    val taskId: String,
    val status: Status,
    val imageUrl: String?
) {
    /**
     * 状态
     */
    enum class Status(@JsonValue val value: Int) {
        /**
         * 排队中
         */
        QUEUEING(0),

        /**
         * 成功
         */
        SUCCESS(1),

        /**
         * 准备中
         */
        PREPARATION(2),

        /**
         * 等待中
         */
        WAITING(3),

        /**
         * 处理中
         */
        PROCESSING(4);

    }

}
