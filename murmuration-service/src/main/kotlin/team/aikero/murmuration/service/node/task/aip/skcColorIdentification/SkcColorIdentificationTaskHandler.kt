package team.aikero.murmuration.service.node.task.aip.skcColorIdentification

import team.aikero.murmuration.common.req.task.SkcColorIdentificationRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult

/**
 * SKC颜色识别任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.SKC_COLOR_IDENTIFICATION
)
class SkcColorIdentificationTaskHandler(
    val executor: SkcColorIdentificationExecutor,
): TaskHandler<SkcColorIdentificationRequest, String> {
    override fun create(request: SkcColorIdentificationRequest): String {
        return executor.createTask(
            SkcColorIdentificationInput(
                url = request.url,
                pinlei = request.pinlei,
                outputUrl = request.outputUrl,
                spuid = request.spuid,
                skcid = request.skcid
            )
        )
    }

    override fun query(request: SkcColorIdentificationRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map {
            // 将颜色识别结果作为属性返回，URL字段使用结果中的output_url或占位符
            val urlResult = it.outputUrl ?: "skc-color-identification-result"
            listOf(TaskHandlerResult(urlResult, it))
        }
    }
}