package team.aikero.murmuration.service.node.task.openbridge.image_cropping

import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.common.req.task.ai_box.RemoveWatermarkHandlerRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskImmediatelyDoneException
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.openbridge.OpenBridgeClient
import team.aikero.murmuration.service.node.task.openbridge.imageCroppingRequest
import team.aikero.murmuration.service.utils.transferFrom
import java.net.URI

/**
 * 图片裁剪
 */
@TaskIdentifier(
    supplier = Supplier.AIDGE,
    ability = Ability.IMAGE_CROPPING
)
class ImageCroppingTaskHandler(
    private val openBridgeClient: OpenBridgeClient,
    private val ossTemplate: OssTemplate
) : TaskHandler<imageCroppingRequest, String> {
    override fun create(request: imageCroppingRequest): String {
        val imageUrl =
            openBridgeClient.createRemoveWaterMark(ossTemplate.transferFrom(request.imageUrl))
        throw TaskImmediatelyDoneException(listOf(TaskHandlerResult.image(imageUrl)))
    }

    override fun query(
        request: imageCroppingRequest,
        context: String
    ): TaskResult<List<TaskHandlerResult>> {
        TODO("任务立即完成，不需要执行 query")
    }

}