package team.aikero.murmuration.service.node.task.aip.aeAttributeConvert

import team.aikero.murmuration.service.node.task.aip.*

@AipExecutorIdentifier(AipAbility.AE_PUTAWAY_ATTRIBUTE_CONVERT)
class AeAttributeConvertExecutor(client: AipClient): AipExecutor<AeAttributeConvertInput, AeAttributeConvertOutput>(client) {
    override fun validateTaskOutput(output: AeAttributeConvertOutput) {
        if (output.id.isBlank()) {
            throw InvalidTaskOutputException("id 不能为空")
        }
        // 其他字段除了id外，工作流生成失败时可能为null，不做强制验证
    }
}

data class AeAttributeConvertInput(
    /**
     * 产品ID
     */
    val id: String,

    /**
     * 产品名称
     */
    val productName: String,

    /**
     * 产品类型
     */
    val productType: String,

    /**
     * 产品信息
     */
    val productInfo: String,

    /**
     * 主图URL
     */
    val masterImgUrl: String,

    /**
     * Muse属性映射表URL
     */
    val museAttrXlsxUrl: String,

    /**
     * AE批量模板URL
     */
    val aeTemplateXlsxUrl: String,
)

data class AeAttributeConvertOutput(
    /**
     * SPU ID（不为空）
     */
    val id: String,

    /**
     * 商品名称
     */
    val name: String?,

    /**
     * 商品品类名
     */
    val type: String?,

    /**
     * 商品属性列表
     */
    val info: String?,

    /**
     * 属性转换结果 - JSON对象
     */
    val resAttr: String?
)